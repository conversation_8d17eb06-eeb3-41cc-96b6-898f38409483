# 使用官方的 Python 镜像作为基础镜像
FROM pytorch/pytorch:2.3.1-cuda11.8-cudnn8-runtime

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    nginx \
    supervisor \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt requirements.txt

# 安装 Python 依赖项
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用文件
COPY main.py .
COPY index.html .

# 下载模型
RUN python -c "from funasr import AutoModel; AutoModel(model='paraformer-zh',vad_model='fsmn-vad',punc_model='ct-punc')"

# 配置 Nginx
COPY nginx.conf /etc/nginx/sites-available/default

# 配置 Supervisor
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 创建前端文件目录并复制前端文件
RUN mkdir -p /var/www/html
COPY index.html /var/www/html/

# 暴露端口 (80 for frontend, 12369 for backend API)
EXPOSE 80 12369

# 使用 Supervisor 启动多个服务
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]