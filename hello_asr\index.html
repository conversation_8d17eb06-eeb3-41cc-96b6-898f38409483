<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别服务</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.min.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios@1.6.0/dist/axios.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 15px;
            padding: 40px 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #fafafa;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f2ff;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 48px;
            color: #c0c4cc;
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: #606266;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #909399;
            font-size: 14px;
        }
        
        .result-section {
            margin-top: 30px;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }
        
        .result-text {
            font-size: 16px;
            line-height: 1.8;
            color: #303133;
            word-break: break-all;
            white-space: pre-wrap;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .header p {
                font-size: 14px;
            }
            
            .upload-area {
                padding: 30px 15px;
            }
            
            .upload-icon {
                font-size: 36px;
            }
            
            .upload-text {
                font-size: 14px;
            }
            
            .result-text {
                font-size: 14px;
            }
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #e8f4fd;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .file-name {
            color: #409eff;
            font-weight: 500;
        }
        
        .progress-section {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="main-card">
                <div class="header">
                    <h1>🎤 语音识别服务</h1>
                    <p>支持音频和视频文件的语音转文字</p>
                </div>
                
                <div class="content">
                    <!-- 文件上传区域 -->
                    <div class="upload-section">
                        <div class="upload-area" 
                             @click="triggerFileInput"
                             @dragover.prevent="handleDragOver"
                             @dragleave.prevent="handleDragLeave"
                             @drop.prevent="handleDrop"
                             :class="{ dragover: isDragOver }">
                            <div class="upload-icon">
                                <i class="el-icon-upload"></i>
                            </div>
                            <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                            <div class="upload-hint">支持 MP3、WAV、MP4 等音视频格式</div>
                        </div>
                        
                        <input type="file" 
                               ref="fileInput" 
                               @change="handleFileSelect" 
                               accept="audio/*,video/*"
                               style="display: none;">
                    </div>
                    
                    <!-- 文件信息 -->
                    <div v-if="selectedFile" class="file-info">
                        <div>
                            <i class="el-icon-document"></i>
                            <span class="file-name">{{ selectedFile.name }}</span>
                            <span style="color: #909399; margin-left: 10px;">
                                ({{ formatFileSize(selectedFile.size) }})
                            </span>
                        </div>
                        <el-button type="primary" 
                                   @click="uploadFile" 
                                   :loading="isUploading"
                                   size="small">
                            {{ isUploading ? '识别中...' : '开始识别' }}
                        </el-button>
                    </div>
                    
                    <!-- 进度条 -->
                    <div v-if="isUploading" class="progress-section">
                        <el-progress :percentage="uploadProgress" 
                                     :status="uploadProgress === 100 ? 'success' : ''"
                                     :stroke-width="8">
                        </el-progress>
                        <p style="text-align: center; margin-top: 10px; color: #909399;">
                            {{ progressText }}
                        </p>
                    </div>
                    
                    <!-- 识别结果 -->
                    <div v-if="result" class="result-section">
                        <h3 style="color: #303133; margin-bottom: 15px;">
                            <i class="el-icon-success" style="color: #67c23a;"></i>
                            识别结果
                        </h3>
                        <div class="result-card">
                            <div class="result-text">{{ result }}</div>
                            <div style="margin-top: 20px; text-align: right;">
                                <el-button size="small" @click="copyResult">
                                    <i class="el-icon-copy-document"></i>
                                    复制文本
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedFile: null,
                    isUploading: false,
                    uploadProgress: 0,
                    progressText: '',
                    result: '',
                    isDragOver: false
                }
            },
            methods: {
                triggerFileInput() {
                    this.$refs.fileInput.click();
                },
                
                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.selectedFile = file;
                        this.result = '';
                    }
                },
                
                handleDragOver(event) {
                    this.isDragOver = true;
                },
                
                handleDragLeave(event) {
                    this.isDragOver = false;
                },
                
                handleDrop(event) {
                    this.isDragOver = false;
                    const files = event.dataTransfer.files;
                    if (files.length > 0) {
                        this.selectedFile = files[0];
                        this.result = '';
                    }
                },
                
                formatFileSize(bytes) {
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },
                
                async uploadFile() {
                    if (!this.selectedFile) {
                        this.$message.warning('请先选择文件');
                        return;
                    }
                    
                    this.isUploading = true;
                    this.uploadProgress = 0;
                    this.progressText = '正在上传文件...';
                    this.result = '';
                    
                    const formData = new FormData();
                    formData.append('file', this.selectedFile);
                    
                    try {
                        const response = await axios.post('http://localhost:12369/asr', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            },
                            onUploadProgress: (progressEvent) => {
                                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                                this.uploadProgress = Math.min(progress, 90);
                                if (progress < 100) {
                                    this.progressText = '正在上传文件...';
                                } else {
                                    this.progressText = '正在进行语音识别，请稍候...';
                                }
                            }
                        });
                        
                        this.uploadProgress = 100;
                        this.progressText = '识别完成！';
                        
                        if (response.data && response.data.text) {
                            this.result = response.data.text;
                            this.$message.success('语音识别完成！');
                        } else {
                            this.$message.error('识别结果为空');
                        }
                        
                    } catch (error) {
                        console.error('上传失败:', error);
                        this.$message.error('识别失败: ' + (error.response?.data?.detail || error.message));
                    } finally {
                        this.isUploading = false;
                        setTimeout(() => {
                            this.uploadProgress = 0;
                            this.progressText = '';
                        }, 2000);
                    }
                },
                
                copyResult() {
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(this.result).then(() => {
                            this.$message.success('文本已复制到剪贴板');
                        });
                    } else {
                        // 兼容旧浏览器
                        const textArea = document.createElement('textarea');
                        textArea.value = this.result;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        this.$message.success('文本已复制到剪贴板');
                    }
                }
            }
        });
    </script>
</body>
</html>
