<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别服务</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.min.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios@1.6.0/dist/axios.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 15px;
            padding: 40px 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #fafafa;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f2ff;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 48px;
            color: #c0c4cc;
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: #606266;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #909399;
            font-size: 14px;
        }
        
        .result-section {
            margin-top: 30px;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }
        
        .result-text {
            font-size: 16px;
            line-height: 1.8;
            color: #303133;
            word-break: break-all;
            white-space: pre-wrap;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .header p {
                font-size: 14px;
            }
            
            .upload-area {
                padding: 30px 15px;
            }
            
            .upload-icon {
                font-size: 36px;
            }
            
            .upload-text {
                font-size: 14px;
            }
            
            .result-text {
                font-size: 14px;
            }
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #e8f4fd;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .file-name {
            color: #409eff;
            font-weight: 500;
        }
        
        .progress-section {
            margin: 20px 0;
        }

        .history-section {
            margin-top: 40px;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #909399;
            transition: all 0.3s ease;
        }

        .history-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .history-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 12px;
            color: #909399;
        }

        .history-text {
            font-size: 14px;
            line-height: 1.6;
            color: #303133;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .history-actions {
            margin-top: 15px;
            text-align: right;
        }

        .empty-history {
            text-align: center;
            padding: 40px 20px;
            color: #909399;
        }

        .empty-history i {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="main-card">
                <div class="header">
                    <h1>🎤 语音识别服务</h1>
                    <p>支持音频和视频文件的语音转文字</p>
                </div>
                
                <div class="content">
                    <!-- 文件上传区域 -->
                    <div class="upload-section">
                        <div class="upload-area" 
                             @click="triggerFileInput"
                             @dragover.prevent="handleDragOver"
                             @dragleave.prevent="handleDragLeave"
                             @drop.prevent="handleDrop"
                             :class="{ dragover: isDragOver }">
                            <div class="upload-icon">
                                <i class="el-icon-upload"></i>
                            </div>
                            <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                            <div class="upload-hint">支持 MP3、WAV、MP4 等音视频格式</div>
                        </div>
                        
                        <input type="file" 
                               ref="fileInput" 
                               @change="handleFileSelect" 
                               accept="audio/*,video/*"
                               style="display: none;">
                    </div>
                    
                    <!-- 文件信息 -->
                    <div v-if="selectedFile" class="file-info">
                        <div>
                            <i class="el-icon-document"></i>
                            <span class="file-name">{{ selectedFile.name }}</span>
                            <span style="color: #909399; margin-left: 10px;">
                                ({{ formatFileSize(selectedFile.size) }})
                            </span>
                        </div>
                        <el-button type="primary" 
                                   @click="uploadFile" 
                                   :loading="isUploading"
                                   size="small">
                            {{ isUploading ? '识别中...' : '开始识别' }}
                        </el-button>
                    </div>
                    
                    <!-- 进度条 -->
                    <div v-if="isUploading" class="progress-section">
                        <el-progress :percentage="uploadProgress" 
                                     :status="uploadProgress === 100 ? 'success' : ''"
                                     :stroke-width="8">
                        </el-progress>
                        <p style="text-align: center; margin-top: 10px; color: #909399;">
                            {{ progressText }}
                        </p>
                    </div>
                    
                    <!-- 识别结果 -->
                    <div v-if="result" class="result-section">
                        <h3 style="color: #303133; margin-bottom: 15px;">
                            <i class="el-icon-success" style="color: #67c23a;"></i>
                            识别结果
                        </h3>
                        <div class="result-card">
                            <div class="result-text">{{ result }}</div>
                            <div style="margin-top: 20px; text-align: right;">
                                <el-button size="small" @click="copyResult">
                                    <i class="el-icon-copy-document"></i>
                                    复制文本
                                </el-button>
                                <el-button size="small" type="primary" @click="saveToHistory" style="margin-left: 10px;">
                                    <i class="el-icon-folder-add"></i>
                                    保存到历史
                                </el-button>
                            </div>
                        </div>
                    </div>

                    <!-- 历史记录 -->
                    <div class="history-section">
                        <div class="stats-card" v-if="historyList.length > 0">
                            <div class="stats-number">{{ historyList.length }}</div>
                            <div class="stats-label">历史记录总数</div>
                        </div>

                        <div class="history-header">
                            <h3 style="color: #303133; margin: 0;">
                                <i class="el-icon-time"></i>
                                历史记录
                            </h3>
                            <div v-if="historyList.length > 0">
                                <el-button size="small" @click="exportHistory">
                                    <i class="el-icon-download"></i>
                                    导出
                                </el-button>
                                <el-button size="small" type="danger" @click="clearHistory">
                                    <i class="el-icon-delete"></i>
                                    清空
                                </el-button>
                            </div>
                        </div>

                        <div v-if="historyList.length === 0" class="empty-history">
                            <i class="el-icon-document-copy"></i>
                            <div>暂无历史记录</div>
                            <div style="font-size: 12px; margin-top: 5px;">识别完成后点击"保存到历史"即可保存记录</div>
                        </div>

                        <div v-else>
                            <div v-for="(item, index) in paginatedHistory" :key="item.id" class="history-item">
                                <div class="history-meta">
                                    <span>
                                        <i class="el-icon-document"></i>
                                        {{ item.fileName }}
                                    </span>
                                    <span>{{ item.timestamp }}</span>
                                </div>
                                <div class="history-text">{{ item.text }}</div>
                                <div class="history-actions">
                                    <el-button size="mini" @click="copyHistoryItem(item.text)">
                                        <i class="el-icon-copy-document"></i>
                                        复制
                                    </el-button>
                                    <el-button size="mini" type="danger" @click="deleteHistoryItem(item.id)">
                                        <i class="el-icon-delete"></i>
                                        删除
                                    </el-button>
                                </div>
                            </div>

                            <!-- 分页 -->
                            <div style="text-align: center; margin-top: 20px;" v-if="historyList.length > pageSize">
                                <el-pagination
                                    @current-change="handlePageChange"
                                    :current-page="currentPage"
                                    :page-size="pageSize"
                                    layout="prev, pager, next"
                                    :total="historyList.length"
                                    small>
                                </el-pagination>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    selectedFile: null,
                    isUploading: false,
                    uploadProgress: 0,
                    progressText: '',
                    result: '',
                    isDragOver: false,
                    historyList: [],
                    currentPage: 1,
                    pageSize: 5
                }
            },
            computed: {
                paginatedHistory() {
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return this.historyList.slice(start, end);
                }
            },
            mounted() {
                this.loadHistory();
            },
            methods: {
                triggerFileInput() {
                    this.$refs.fileInput.click();
                },
                
                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.selectedFile = file;
                        this.result = '';
                    }
                },
                
                handleDragOver(event) {
                    this.isDragOver = true;
                },
                
                handleDragLeave(event) {
                    this.isDragOver = false;
                },
                
                handleDrop(event) {
                    this.isDragOver = false;
                    const files = event.dataTransfer.files;
                    if (files.length > 0) {
                        this.selectedFile = files[0];
                        this.result = '';
                    }
                },
                
                formatFileSize(bytes) {
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },
                
                async uploadFile() {
                    if (!this.selectedFile) {
                        this.$message.warning('请先选择文件');
                        return;
                    }
                    
                    this.isUploading = true;
                    this.uploadProgress = 0;
                    this.progressText = '正在上传文件...';
                    this.result = '';
                    
                    const formData = new FormData();
                    formData.append('file', this.selectedFile);
                    
                    try {
                        const response = await axios.post('/asr', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            },
                            onUploadProgress: (progressEvent) => {
                                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                                this.uploadProgress = Math.min(progress, 90);
                                if (progress < 100) {
                                    this.progressText = '正在上传文件...';
                                } else {
                                    this.progressText = '正在进行语音识别，请稍候...';
                                }
                            }
                        });
                        
                        this.uploadProgress = 100;
                        this.progressText = '识别完成！';
                        
                        if (response.data && response.data.text) {
                            this.result = response.data.text;
                            this.$message.success('语音识别完成！');
                        } else {
                            this.$message.error('识别结果为空');
                        }
                        
                    } catch (error) {
                        console.error('上传失败:', error);
                        this.$message.error('识别失败: ' + (error.response?.data?.detail || error.message));
                    } finally {
                        this.isUploading = false;
                        setTimeout(() => {
                            this.uploadProgress = 0;
                            this.progressText = '';
                        }, 2000);
                    }
                },
                
                copyResult() {
                    this.copyText(this.result);
                },

                copyText(text) {
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(text).then(() => {
                            this.$message.success('文本已复制到剪贴板');
                        });
                    } else {
                        // 兼容旧浏览器
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        this.$message.success('文本已复制到剪贴板');
                    }
                },

                saveToHistory() {
                    if (!this.result || !this.selectedFile) {
                        this.$message.warning('没有可保存的内容');
                        return;
                    }

                    const historyItem = {
                        id: Date.now() + Math.random(),
                        fileName: this.selectedFile.name,
                        text: this.result,
                        timestamp: new Date().toLocaleString('zh-CN'),
                        createTime: Date.now()
                    };

                    this.historyList.unshift(historyItem);
                    this.saveHistory();
                    this.$message.success('已保存到历史记录');
                },

                loadHistory() {
                    try {
                        const saved = localStorage.getItem('asr_history');
                        if (saved) {
                            this.historyList = JSON.parse(saved);
                            // 按时间倒序排列
                            this.historyList.sort((a, b) => b.createTime - a.createTime);
                        }
                    } catch (error) {
                        console.error('加载历史记录失败:', error);
                        this.historyList = [];
                    }
                },

                saveHistory() {
                    try {
                        localStorage.setItem('asr_history', JSON.stringify(this.historyList));
                    } catch (error) {
                        console.error('保存历史记录失败:', error);
                        this.$message.error('保存失败，可能是存储空间不足');
                    }
                },

                copyHistoryItem(text) {
                    this.copyText(text);
                },

                deleteHistoryItem(id) {
                    this.$confirm('确定要删除这条记录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.historyList = this.historyList.filter(item => item.id !== id);
                        this.saveHistory();
                        this.$message.success('删除成功');

                        // 如果当前页没有数据了，回到上一页
                        const totalPages = Math.ceil(this.historyList.length / this.pageSize);
                        if (this.currentPage > totalPages && totalPages > 0) {
                            this.currentPage = totalPages;
                        }
                    }).catch(() => {
                        // 用户取消删除
                    });
                },

                clearHistory() {
                    this.$confirm('确定要清空所有历史记录吗？此操作不可恢复！', '警告', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.historyList = [];
                        this.currentPage = 1;
                        this.saveHistory();
                        this.$message.success('历史记录已清空');
                    }).catch(() => {
                        // 用户取消清空
                    });
                },

                exportHistory() {
                    if (this.historyList.length === 0) {
                        this.$message.warning('没有可导出的历史记录');
                        return;
                    }

                    let content = '语音识别历史记录\n';
                    content += '导出时间：' + new Date().toLocaleString('zh-CN') + '\n';
                    content += '记录总数：' + this.historyList.length + '\n\n';
                    content += '=' * 50 + '\n\n';

                    this.historyList.forEach((item, index) => {
                        content += `记录 ${index + 1}:\n`;
                        content += `文件名：${item.fileName}\n`;
                        content += `时间：${item.timestamp}\n`;
                        content += `内容：${item.text}\n\n`;
                        content += '-' * 30 + '\n\n';
                    });

                    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `语音识别历史记录_${new Date().toISOString().slice(0, 10)}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    this.$message.success('历史记录已导出');
                },

                handlePageChange(page) {
                    this.currentPage = page;
                }
            }
        });
    </script>
</body>
</html>
